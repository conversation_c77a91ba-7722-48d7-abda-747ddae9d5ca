
# TODO: 当日交易请求组件拆分与功能增强方案

## 1. 目标

本文档旨在规划将 `app/src/pages/trade/execute.vue`页面中的“当日交易请求”展示部分，重构为一个独立的、可复用的Vue组件。同时，将根据 `app/src/types/trade-request.ts` 中的 `ITradeRequest` 接口，极大地丰富信息展示，提升用户体验。

## 2. 核心问题与改进方向

- **当前问题**: 目前的列表只显示类型、请求数量、请求价格和状态，信息维度单一，无法满足用户快速了解请求详情（如成交情况、失败原因等）的需求。
- **改进方向**:
    1.  **组件化**: 将展示逻辑封装到独立的 `TradeRequestCard.vue` 或 `TradeRequestList.vue` 组件中，实现关注点分离。
    2.  **信息增强**: 在新组件中，充分利用 `ITradeRequest` 的字段，展示更全面的交易请求信息。
    3.  **优化UI/UX**: 采用卡片式设计，使每个请求的信息结构化、一目了然，并对不同状态使用更清晰的视觉区分。

## 3. 待办任务 (TODO)

### 任务一：创建新的组件 `app/src/components/TradeRequestItem.vue`

这个组件将负责渲染单条交易请求的卡片。

**文件路径**: `app/src/components/TradeRequestItem.vue`

**组件设计**:

1.  **Props**:
    -   `request`: 类型为 `ITradeRequest`，接收单条交易请求的数据。

2.  **Template (建议结构)**:
    -   使用一个根 `view` 作为卡片容器，添加圆角、阴影和内边距。
    -   **卡片头部**:
        -   左侧：显示请求类型（点价/洗基差）和请求时间（`CreatedAt`，格式化为 `HH:mm:ss`）。
        -   右侧：使用一个醒目的彩色徽章（Badge）展示请求状态 (`status`)，例如：
            -   `Executing`: 蓝色
            -   `Completed`: 绿色
            -   `Rejected`: 红色
            -   `Cancelled` / `Expired`: 灰色/橙色
    -   **卡片主体 (分两栏)**:
        -   **请求信息 (左栏)**:
            -   标签：`请求`
            -   内容：`{requestedQuantity} 手 @ {requestedPrice}`
        -   **成交信息 (右栏)**:
            -   标签：`成交`
            -   内容：`{executedQuantity} 手 @ {executedPrice}` (如果 `executedQuantity > 0`)
            -   如果未成交，则显示 `-`。
    -   **卡片底部 (附加信息)**:
        -   **执行模式**: 显示 `executionMode` (如：手动、自动)。
        -   **拒绝原因**: 如果 `status` 为 `Rejected`，则显示 `rejectionReason`。
        -   **过期时间**: 如果 `status` 为 `Executing`，则显示 `expiresAt` 的倒计时或具体时间。
        -   **备注**: 如果存在 `notes`，则显示。

3.  **Script**:
    -   引入 `ITradeRequest` 类型。
    -   定义 `props`。
    -   创建 `getTradeRequestStatusText` 和 `getTradeRequestStatusClass` 这两个方法（从 `execute.vue` 迁移过来并增强）。
    -   添加必要的计算属性来格式化日期、价格等。

### 任务二：创建列表容器组件 `app/src/components/TradeRequestList.vue`

这个组件将负责管理和展示整个交易请求列表。

**文件路径**: `app/src/components/TradeRequestList.vue`

**组件设计**:

1.  **Props**:
    -   `requests`: 类型为 `ITradeRequest[]`，接收交易请求数组。
    -   `requestType`: 类型为 `TradeRequestType`，用于显示列表标题（例如“当日点价请求”）。
    -   `loading`: `boolean`，用于显示加载状态。

2.  **Template**:
    -   显示一个标题，如 `当日{{ isPointPrice ? '点价' : '洗基差' }}请求`。
    -   如果 `loading` 为 `true`，显示骨架屏或加载动画。
    -   如果 `requests` 数组为空且不处于加载状态，显示“暂无记录”的提示。
    -   使用 `v-for` 遍历 `requests` 数组，并为每个 `request` 渲染一个 `TradeRequestItem` 组件，通过 prop 传入单条请求数据。

### 任务三：改造父页面 `app/src/pages/trade/execute.vue`

**文件路径**: `app/src/pages/trade/execute.vue`

**调整内容**:

1.  **Script**:
    -   导入新建的 `TradeRequestList.vue` 组件。
    -   在 `components` 中注册。
    -   **删除** `getTradeRequestStatusText` 和 `getTradeRequestStatusClass` 方法，因为它们的功能已经移至子组件。

2.  **Template**:
    -   找到原先的 “当日交易请求概览” 部分。
    -   **删除** 原有的 `v-for` 循环和其内部的 `grid` 布局。
    -   在原位置替换为新的组件：
        ```html
        <TradeRequestList
          :requests="todayTradeRequests"
          :request-type="requestType"
          :loading="/* 如果有加载状态的话 */"
        />
        ```

## 4. 预期收益

- **代码质量提升**:
    -   **关注点分离**: `execute.vue` 页面更专注于业务流程和布局，而列表展示的复杂性被封装在子组件中。
    -   **可维护性**: 修改请求列表的样式或逻辑时，只需在 `TradeRequestItem.vue` 中进行，不会影响到父页面。
    -   **可复用性**: `TradeRequestList.vue` 组件未来可以在其他需要展示交易请求列表的页面（如历史查询）中复用。
- **用户体验提升**:
    -   **信息密度与清晰度**: 用户可以在不离开当前页面的情况下，获得每个请求的完整生命周期信息（请求、成交、状态、原因等）。
    -   **决策支持**: 丰富的上下文信息（如拒绝原因、执行模式）能帮助用户更好地理解交易结果，并为后续操作提供依据。
    -   **现代化UI**: 卡片式设计比简单的文本行更美观，也更符合移动端的设计趋势。
