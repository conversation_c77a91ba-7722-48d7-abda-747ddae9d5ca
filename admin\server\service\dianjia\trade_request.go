package dianjia

import (
	"errors"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"gorm.io/gorm"
)

// MatchResult 匹配结果结构
type MatchResult struct {
	Success bool
	Reason  string
}

type TradeRequestService struct{}

// CreateTradeRequest 创建交易请求 - V4 立即执行模式
func (s *TradeRequestService) CreateTradeRequest(req *dianjia.CreateTradeRequestRequest, pricerID uint) (*dianjia.TradeRequest, error) {
	// 开启数据库事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return nil, err
	}

	// 验证请求参数
	if err := s.validateTradeRequestV4(req); err != nil {
		tx.Rollback()
		return nil, err
	}

	// 立即进行合同可用数量的匹配与冻结
	matchResult, err := s.matchAndFreezeContracts(tx, req)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	// 创建交易请求记录
	var status dianjia.TradeRequestStatus
	var rejectionReason string

	if matchResult.Success {
		status = dianjia.TradeRequestStatusExecuting
	} else {
		status = dianjia.TradeRequestStatusRejected
		rejectionReason = matchResult.Reason
	}

	tradeRequest := &dianjia.TradeRequest{
		PricerID:          pricerID,
		SetterID:          req.SetterID,
		InstrumentRefID:   req.InstrumentRefID,
		RequestType:       req.RequestType,
		RequestedQuantity: req.RequestedQuantity,
		RequestedPrice:    req.RequestedPrice,
		ExecutedQuantity:  0,
		ExecutedPrice:     0,
		Status:            status,
		ExecutionMode:     req.ExecutionMode,
		RejectionReason:   rejectionReason,
		ExpiresAt:         &req.ExpiresAt,
	}

	if err := tx.Create(tradeRequest).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建交易请求失败: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	// 重新加载完整数据
	if err := global.GVA_DB.Preload("Pricer").Preload("Setter").Preload("Instrument").Preload("ExecutionDetails.Contract").First(tradeRequest, tradeRequest.ID).Error; err != nil {
		return nil, err
	}

	return tradeRequest, nil
}

// GetTradeRequest 获取单个交易请求
func (s *TradeRequestService) GetTradeRequest(id uint) (*dianjia.TradeRequest, error) {
	var tradeRequest dianjia.TradeRequest
	err := global.GVA_DB.
		Preload("Pricer").
		Preload("Setter").
		Preload("Instrument").
		Preload("ExecutionDetails.Contract").
		First(&tradeRequest, id).Error

	if err != nil {
		return nil, fmt.Errorf("获取交易请求失败: %v", err)
	}

	return &tradeRequest, nil
}

// CancelTradeRequest 取消交易请求 (V4)
func (s *TradeRequestService) CancelTradeRequest(id uint, userID uint) error {
	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var tradeRequest dianjia.TradeRequest
	if err := tx.First(&tradeRequest, id).Error; err != nil {
		tx.Rollback()
		return errors.New("交易请求不存在")
	}

	// 验证权限
	if tradeRequest.PricerID != userID {
		tx.Rollback()
		return errors.New("无权限取消此交易请求")
	}

	// 只有执行中的请求可以取消
	if tradeRequest.Status != dianjia.TradeRequestStatusExecuting {
		tx.Rollback()
		return errors.New("只有执行中的交易请求可以取消")
	}

	// 解冻所有冻结的合同数量
	if err := s.unfreezeContractsForTradeRequestV4(tx, &tradeRequest); err != nil {
		tx.Rollback()
		return fmt.Errorf("解冻合同失败: %v", err)
	}

	tradeRequest.Status = dianjia.TradeRequestStatusCancelled
	if err := tx.Save(&tradeRequest).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetTradeRequestsForPricer 获取点价方的交易请求列表
func (s *TradeRequestService) GetTradeRequestsForPricer(pricerID uint, req *dianjia.TradeRequestForPricerRequest) ([]dianjia.TradeRequest, int64, error) {
	var tradeRequests []dianjia.TradeRequest
	var total int64

	db := global.GVA_DB.Model(&dianjia.TradeRequest{}).Where("pricer_id = ?", pricerID)

	// 添加筛选条件
	if req.InstrumentRefID != 0 {
		db = db.Where("instrument_ref_id = ?", req.InstrumentRefID)
	}
	if req.RequestType != "" {
		db = db.Where("request_type = ?", req.RequestType)
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.StartDate != "" {
		db = db.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		db = db.Where("created_at <= ?", req.EndDate)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取点价方交易请求总数失败: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err := db.
		Preload("Pricer").
		Preload("Setter").
		Preload("Instrument").
		Preload("ExecutionDetails.Contract").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Find(&tradeRequests).Error

	if err != nil {
		return nil, 0, fmt.Errorf("获取点价方交易请求列表失败: %v", err)
	}

	return tradeRequests, total, nil
}

// GetTradeRequestsForSetter 获取被点价方的交易请求列表
func (s *TradeRequestService) GetTradeRequestsForSetter(setterID uint, req *dianjia.TradeRequestForSetterRequest) ([]dianjia.TradeRequest, int64, error) {
	var tradeRequests []dianjia.TradeRequest
	var total int64

	db := global.GVA_DB.Model(&dianjia.TradeRequest{}).Where("setter_id = ?", setterID)

	// 添加筛选条件
	if req.InstrumentRefID != 0 {
		db = db.Where("instrument_ref_id = ?", req.InstrumentRefID)
	}
	if req.RequestType != "" {
		db = db.Where("request_type = ?", req.RequestType)
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	} else {
		// 默认只显示执行中的请求 (V4)
		db = db.Where("status = ?", string(dianjia.TradeRequestStatusExecuting))
	}
	if req.StartDate != "" {
		db = db.Where("created_at >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		db = db.Where("created_at <= ?", req.EndDate)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取被点价方交易请求总数失败: %v", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	err := db.
		Preload("Pricer").
		Preload("Setter").
		Preload("Instrument").
		Preload("ExecutionDetails.Contract").
		Order("created_at DESC").
		Offset(offset).
		Limit(req.PageSize).
		Find(&tradeRequests).Error

	if err != nil {
		return nil, 0, fmt.Errorf("获取被点价方交易请求列表失败: %v", err)
	}

	return tradeRequests, total, nil
}

// ManualFeedback 人工反馈交易结果 (V4 - 仅处理成交)
func (s *TradeRequestService) ManualFeedback(tradeRequestID uint, setterID uint, req *dianjia.ManualFeedbackRequest) error {
	// 开启数据库事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	// 获取交易请求
	var tradeRequest dianjia.TradeRequest
	if err := tx.First(&tradeRequest, tradeRequestID).Error; err != nil {
		tx.Rollback()
		return errors.New("交易请求不存在")
	}

	// 验证权限：只有被点价方可以处理
	if tradeRequest.SetterID != setterID {
		tx.Rollback()
		return errors.New("无权限处理此交易请求")
	}

	// 验证状态：只有执行中的请求可以操作
	if tradeRequest.Status != dianjia.TradeRequestStatusExecuting {
		tx.Rollback()
		return errors.New("只有执行中的交易请求可以操作")
	}

	// 验证成交数量和价格
	if req.Quantity <= 0 || req.Price <= 0 {
		tx.Rollback()
		return errors.New("成交数量和价格必须大于0")
	}

	remainingQuantity := tradeRequest.RequestedQuantity - tradeRequest.ExecutedQuantity
	if req.Quantity > remainingQuantity {
		tx.Rollback()
		return fmt.Errorf("成交数量不能超过剩余数量 %d", remainingQuantity)
	}

	// 处理合同消耗和生成新合同
	err := s.processContractConsumptionAndGeneration(tx, &tradeRequest, req.Quantity, req.Price)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("处理合同消耗失败: %v", err)
	}

	// 计算新的加权平均执行价格
	newExecutedPrice := s.calculateWeightedAveragePriceV4(
		tradeRequest.ExecutedPrice,
		tradeRequest.ExecutedQuantity,
		req.Price,
		req.Quantity,
	)

	// 更新交易请求的执行数量和价格
	tradeRequest.ExecutedQuantity += req.Quantity
	tradeRequest.ExecutedPrice = newExecutedPrice

	// 检查是否完全成交
	if tradeRequest.ExecutedQuantity >= tradeRequest.RequestedQuantity {
		tradeRequest.Status = dianjia.TradeRequestStatusCompleted
	}
	// 否则保持 Executing 状态，支持继续部分成交

	if err := tx.Save(&tradeRequest).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新交易请求状态失败: %v", err)
	}

	// // 处理合同消耗和生成新合同
	// err = s.processContractConsumptionAndGeneration(tx, &tradeRequest, req.Quantity, req.Price)
	// if err != nil {
	// 	tx.Rollback()
	// 	return fmt.Errorf("处理合同消耗失败: %v", err)
	// }

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// validateTradeRequestV4 验证V4交易请求参数
func (s *TradeRequestService) validateTradeRequestV4(req *dianjia.CreateTradeRequestRequest) error {
	// 验证过期时间
	if req.ExpiresAt.Before(time.Now()) {
		return errors.New("过期时间不能早于当前时间")
	}

	// 验证点价时必须有价格
	if req.RequestType == dianjia.TradeRequestTypePointPrice && req.RequestedPrice <= 0 {
		return errors.New("点价操作必须指定价格")
	}

	return nil
}

// matchAndFreezeContracts 匹配并冻结合同 (V4)
func (s *TradeRequestService) matchAndFreezeContracts(tx *gorm.DB, req *dianjia.CreateTradeRequestRequest) (*MatchResult, error) {
	// 确定要查找的合同类型
	var targetContractType dianjia.ContractPriceType
	if req.RequestType == dianjia.TradeRequestTypePointPrice {
		// 点价操作：查找基差合同
		targetContractType = dianjia.ContractPriceTypeBasis
	} else {
		// 洗基差操作：查找固定价合同
		targetContractType = dianjia.ContractPriceTypeFixed
	}

	// 查找所有符合条件的活跃合同，按创建时间排序（FIFO）
	var availableContracts []dianjia.Contract
	err := tx.Where("instrument_ref_id = ? AND price_type = ? AND status = ? AND remaining_quantity > 0 and setter_id = ? AND remaining_quantity - frozen_quantity > 0",
		req.InstrumentRefID,
		targetContractType,
		dianjia.ContractStatusExecuting,
		req.SetterID,
	).Order("created_at ASC").Find(&availableContracts).Error

	if err != nil {
		return nil, fmt.Errorf("查找可用合同失败: %v", err)
	}

	// 检查是否有足够的合同数量
	totalAvailable := 0
	for _, contract := range availableContracts {
		availabelVolume := contract.RemainingQuantity - contract.FrozenQuantity
		totalAvailable += availabelVolume
	}

	if totalAvailable < req.RequestedQuantity {
		return &MatchResult{
			Success: false,
			Reason:  fmt.Sprintf("可用数量不足，需要 %d，可用 %d", req.RequestedQuantity, totalAvailable),
		}, nil
	}

	// 冻结合同数量
	remainingToFreeze := req.RequestedQuantity
	for _, contract := range availableContracts {
		if remainingToFreeze <= 0 {
			break
		}

		freezeQuantity := contract.RemainingQuantity - contract.FrozenQuantity

		if freezeQuantity <= 0 {
			continue
		}

		if freezeQuantity > remainingToFreeze {
			freezeQuantity = remainingToFreeze
		}

		// 更新合同的冻结数量
		err = tx.Model(&contract).Updates(map[string]interface{}{
			"frozen_quantity": contract.FrozenQuantity + freezeQuantity,
		}).Error

		if err != nil {
			return nil, fmt.Errorf("冻结合同数量失败: %v", err)
		}

		remainingToFreeze -= freezeQuantity
	}

	return &MatchResult{Success: true}, nil
}

// RejectTradeRequest 拒绝交易请求 (V4新增)
func (s *TradeRequestService) RejectTradeRequest(id uint, userID uint, req *dianjia.RejectTradeRequestRequest) error {
	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var tradeRequest dianjia.TradeRequest
	if err := tx.First(&tradeRequest, id).Error; err != nil {
		tx.Rollback()
		return errors.New("交易请求不存在")
	}

	// 验证权限 - 只有被点价方可以拒绝
	if tradeRequest.SetterID != userID {
		tx.Rollback()
		return errors.New("无权限拒绝此交易请求")
	}

	// 只有执行中的请求可以拒绝
	if tradeRequest.Status != dianjia.TradeRequestStatusExecuting {
		tx.Rollback()
		return errors.New("只有执行中的交易请求可以拒绝")
	}

	// 解冻所有冻结的合同数量
	if err := s.unfreezeContractsForTradeRequestV4(tx, &tradeRequest); err != nil {
		tx.Rollback()
		return fmt.Errorf("解冻合同失败: %v", err)
	}

	// 更新状态和拒绝理由
	tradeRequest.Status = dianjia.TradeRequestStatusRejected
	tradeRequest.RejectionReason = req.Reason
	if err := tx.Save(&tradeRequest).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// unfreezeContractsForTradeRequestV4 解冻交易请求相关的合同数量 (V4)
func (s *TradeRequestService) unfreezeContractsForTradeRequestV4(tx *gorm.DB, tradeRequest *dianjia.TradeRequest) error {
	// 确定要查找的合同类型
	var targetContractType dianjia.ContractPriceType
	if tradeRequest.RequestType == dianjia.TradeRequestTypePointPrice {
		targetContractType = dianjia.ContractPriceTypeBasis
	} else {
		targetContractType = dianjia.ContractPriceTypeFixed
	}

	// 查找所有相关的合同
	var contracts []dianjia.Contract
	err := tx.Where("instrument_ref_id = ? AND price_type = ? AND frozen_quantity > 0 AND setter_id = ?",
		tradeRequest.InstrumentRefID,
		targetContractType,
		tradeRequest.SetterID,
	).Order("created_at ASC").Find(&contracts).Error

	if err != nil {
		return fmt.Errorf("查找相关合同失败: %v", err)
	}

	// 计算需要解冻的总数量
	remainingToUnfreeze := tradeRequest.RequestedQuantity - tradeRequest.ExecutedQuantity

	// 解冻合同数量
	for _, contract := range contracts {
		if remainingToUnfreeze <= 0 {
			break
		}

		unfreezeQuantity := contract.FrozenQuantity
		if unfreezeQuantity > remainingToUnfreeze {
			unfreezeQuantity = remainingToUnfreeze
		}

		// 更新合同的冻结数量
		err = tx.Model(&contract).Updates(map[string]interface{}{
			"frozen_quantity": contract.FrozenQuantity - unfreezeQuantity,
		}).Error

		if err != nil {
			return fmt.Errorf("解冻合同数量失败: %v", err)
		}

		remainingToUnfreeze -= unfreezeQuantity
	}

	return nil
}

// calculateWeightedAveragePriceV4 计算加权平均价格 (V4)
func (s *TradeRequestService) calculateWeightedAveragePriceV4(oldPrice float64, oldQuantity int, newPrice float64, newQuantity int) float64 {
	if oldQuantity == 0 {
		return newPrice
	}

	totalValue := (oldPrice * float64(oldQuantity)) + (newPrice * float64(newQuantity))
	totalQuantity := float64(oldQuantity + newQuantity)

	return totalValue / totalQuantity
}

// findOrCreateTargetContract 找出或创建目标生成合同
func (s *TradeRequestService) findOrCreateTargetContract(tx *gorm.DB, tradeRequest *dianjia.TradeRequest, targetContractType dianjia.ContractPriceType) (*dianjia.Contract, error) {
	// 生成目标合同编号
	targetContractCode := fmt.Sprintf("GEN-%d", tradeRequest.ID)

	// 查找是否已存在目标合同
	var targetContract dianjia.Contract
	err := tx.Where("source_trade_request_id = ?", tradeRequest.ID).First(&targetContract).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("查询目标合同失败: %v", err)
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新的目标合同（空白状态，pending）
		targetContract = dianjia.Contract{
			ContractCode:         targetContractCode,
			SetterID:             tradeRequest.SetterID,
			PricerID:             tradeRequest.PricerID,
			InstrumentRefID:      tradeRequest.InstrumentRefID,
			TotalQuantity:        0,
			RemainingQuantity:    0,
			FrozenQuantity:       0,
			PriceType:            targetContractType,
			PriceValue:           0, // 初始价格为0，后续计算加权平均价
			Status:               "Pending",
			SourceTradeRequestID: &tradeRequest.ID,
			IsGenerated:          true,
			Remarks:              fmt.Sprintf("由交易请求 %d 生成", tradeRequest.ID),
		}

		if err := tx.Create(&targetContract).Error; err != nil {
			return nil, fmt.Errorf("创建目标合同失败: %v", err)
		}
	}

	return &targetContract, nil
}

// updateTargetContract 更新目标合同的价格和数量
func (s *TradeRequestService) updateTargetContract(tx *gorm.DB, targetContract *dianjia.Contract, addQuantity int, addTotalValue float64) error {
	// 计算新的加权平均价格
	currentTotalValue := targetContract.PriceValue * float64(targetContract.TotalQuantity)
	newTotalValue := currentTotalValue + addTotalValue
	newTotalQuantity := targetContract.TotalQuantity + addQuantity

	if newTotalQuantity > 0 {
		targetContract.PriceValue = newTotalValue / float64(newTotalQuantity)
	}

	// 更新数量
	targetContract.TotalQuantity = newTotalQuantity
	targetContract.RemainingQuantity += addQuantity

	// 保存更新
	if err := tx.Save(targetContract).Error; err != nil {
		return fmt.Errorf("保存目标合同失败: %v", err)
	}

	return nil
}

// processContractConsumptionAndGeneration 处理合同消耗和生成新合同 (V4)
func (s *TradeRequestService) processContractConsumptionAndGeneration(tx *gorm.DB, tradeRequest *dianjia.TradeRequest, quantity int, price float64) error {
	// 1. 找出所有符合条件的冻结合同
	var consumContractType dianjia.ContractPriceType
	var targetContractType dianjia.ContractPriceType

	if tradeRequest.RequestType == dianjia.TradeRequestTypePointPrice {
		consumContractType = dianjia.ContractPriceTypeBasis // 消耗基差合同
		targetContractType = dianjia.ContractPriceTypeFixed // 生成固定价合同
	} else {
		consumContractType = dianjia.ContractPriceTypeFixed // 消耗固定价合同
		targetContractType = dianjia.ContractPriceTypeBasis // 生成基差合同
	}

	// 查找所有相关的冻结合同，按创建时间排序
	var contracts []dianjia.Contract
	err := tx.Where("instrument_ref_id = ? AND price_type = ? AND frozen_quantity > 0 AND setter_id = ?",
		tradeRequest.InstrumentRefID,
		consumContractType,
		tradeRequest.SetterID,
	).Order("created_at ASC").Find(&contracts).Error

	if err != nil {
		return fmt.Errorf("查找冻结合同失败: %v", err)
	}

	// 2. 找出或创建目标生成合同
	targetContract, err := s.findOrCreateTargetContract(tx, tradeRequest, targetContractType)
	if err != nil {
		return fmt.Errorf("找出或创建目标合同失败: %v", err)
	}

	// 3. 逐条消耗合同并生成成交明细
	remainingToConsume := quantity
	totalConsumedValue := 0.0 // 用于计算加权平均价

	for i := range contracts {
		if remainingToConsume <= 0 {
			break
		}

		contract := &contracts[i]

		// 计算本次从该合同消耗的数量
		consumeQuantity := contract.FrozenQuantity
		if consumeQuantity > remainingToConsume {
			consumeQuantity = remainingToConsume
		}

		// 计算目标价格
		var resultPrice float64
		if tradeRequest.RequestType == dianjia.TradeRequestTypePointPrice {
			// 点价操作：固定价 = 成交价 + 基差
			resultPrice = price + contract.PriceValue
		} else {
			// 洗基差操作：基差 = 固定价 - 成交价
			resultPrice = contract.PriceValue - price
		}

		// 创建执行明细记录
		executionDetail := &dianjia.ExecutionDetail{
			TradeRequestID:   tradeRequest.ID,
			ContractID:       contract.ID,
			ExecutedQuantity: consumeQuantity,
			ExecutedPrice:    price,
			ContractPrice:    contract.PriceValue,
			ResultPrice:      resultPrice,
			ExecutionType:    "MANUAL",
			Status:           "Success",
			Remarks:          fmt.Sprintf("人工成交 - 消耗合同 %s", contract.ContractCode),
		}

		if err := tx.Create(executionDetail).Error; err != nil {
			return fmt.Errorf("创建执行明细失败: %v", err)
		}

		// 同步减去冻结数量和剩余数量
		contract.FrozenQuantity -= consumeQuantity
		contract.RemainingQuantity -= consumeQuantity

		// 当剩余数量为0时，更新合同状态为完成
		if contract.RemainingQuantity == 0 {
			contract.Status = dianjia.ContractStatusCompleted
		}

		// 保存合同更新
		if err := tx.Save(contract).Error; err != nil {
			return fmt.Errorf("更新合同状态失败: %v", err)
		}

		// 累计消耗的价值，用于计算加权平均价
		totalConsumedValue += resultPrice * float64(consumeQuantity)
		remainingToConsume -= consumeQuantity
	}

	if remainingToConsume > 0 {
		return fmt.Errorf("剩余数量不足")
	}

	// 4. 更新目标合同的价格和数量
	err = s.updateTargetContract(tx, targetContract, quantity, totalConsumedValue)
	if err != nil {
		return fmt.Errorf("更新目标合同失败: %v", err)
	}

	// 5. 如果交易请求完成，更新合同状态
	if tradeRequest.ExecutedQuantity+quantity >= tradeRequest.RequestedQuantity {
		targetContract.Status = dianjia.ContractStatusExecuting
		if err := tx.Save(targetContract).Error; err != nil {
			return fmt.Errorf("更新目标合同状态失败: %v", err)
		}
	}

	return nil
}
