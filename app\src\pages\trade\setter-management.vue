<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "交易请求管理",
    "enablePullDownRefresh": true,
    "backgroundTextStyle": "dark",
    "onReachBottomDistance": 50,
    "backgroundColor": "#f7f8fa",
    "pullDownRefresh": {
      "color": "#409eff",
      "offset": 80
    }
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'

import type { ITradeRequest, TradeRequestStatus, IManualFeedbackRequest, IRejectTradeRequestRequest } from '@/types/trade-request'
import { getMyTradeRequestsAsSetter, manualFeedback, rejectTradeRequest } from '@/api/traderequest'
import { toast } from '@/utils/toast'
import TradeRequestList from '@/components/TradeRequestList.vue'
defineOptions({
  name: 'SetterManagementPage',
})



// 响应式状态
const tradeRequests = ref<ITradeRequest[]>([])
const loading = ref(false)
const refreshing = ref(false)
const loadingMore = ref(false)
const statusFilter = ref<TradeRequestStatus | ''>('')

// 分页状态
const page = ref(1)
const pageSize = ref(50)
const hasMore = ref(true)
const feedbackDialogVisible = ref(false)
const currentRequest = ref<ITradeRequest | null>(null)
const feedbackForm = ref<IManualFeedbackRequest>({
  quantity: 0,
  price: 0,
  notes: ''
})

const rejectForm = ref<IRejectTradeRequestRequest>({
  reason: ''
})

const currentAction = ref<'fill' | 'reject' | 'convertToSimulation' | 'convertToTrade'>('fill')

// 状态筛选选项 (V4)
const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '执行中', value: 'Executing' },
  { label: '已完成', value: 'Completed' },
  { label: '已拒绝', value: 'Rejected' },
  { label: '已取消', value: 'Cancelled' },
  { label: '已过期', value: 'Expired' }
])

// 计算属性
const isEmpty = computed(() => {
  return !loading.value && !refreshing.value && tradeRequests.value.length === 0
})

const loadMoreText = computed(() => {
  if (loadingMore.value) {
    return '加载中...'
  }
  if (!hasMore.value) {
    return '没有更多数据了'
  }
  return '上拉加载更多'
})

const currentMode = computed(() => {
  return 'setter' as const
})

const pageTitle = computed(() => {
  return '交易请求管理'
})


// 加载交易请求列表
async function loadTradeRequests(isRefresh = false) {
  // 防止重复加载
  if ((loading.value || loadingMore.value) && !isRefresh) return

  // 如果是刷新，重置分页状态
  if (isRefresh) {
    refreshing.value = true
    page.value = 1
    hasMore.value = true
  } else if (page.value === 1) {
    loading.value = true
  } else {
    loadingMore.value = true
  }
  
  try {
    const currentPage = isRefresh ? 1 : page.value
    const response = await getMyTradeRequestsAsSetter({
      status: statusFilter.value || undefined,
      page: currentPage,
      pageSize: pageSize.value
    })
    
    if (response.code === 0) {
      const newRequests = response.data.list
      
      if (isRefresh || page.value === 1) {
        // 刷新或首次加载，替换数据
        tradeRequests.value = newRequests
        if (isRefresh) {
          toast.success('刷新成功')
        }
      } else {
        // 加载更多，追加数据
        tradeRequests.value.push(...newRequests)
      }

      // 更新分页状态
      hasMore.value = newRequests.length === pageSize.value
    } else {
      toast.error(response.msg || '获取交易请求失败')
    }
  } catch (error) {
    console.error('获取交易请求失败:', error)
    toast.error('网络错误')
  } finally {
    // 确保在所有情况下都停止下拉刷新
    if (isRefresh) {
      console.log('停止下拉刷新')
      refreshing.value = false
      // 使用 nextTick 确保 DOM 更新后再停止刷新
      nextTick(() => {
        uni.stopPullDownRefresh()
        console.log('uni.stopPullDownRefresh() 已调用')
      })
    } else if (page.value === 1) {
      loading.value = false
    } else {
      loadingMore.value = false
    }
  }
}

// 加载更多数据
async function loadMore() {
  if (!hasMore.value || loadingMore.value || loading.value) return

  page.value++
  await loadTradeRequests()
}

// 处理状态筛选
function handleStatusFilter() {
  // 重置分页状态
  page.value = 1
  hasMore.value = true
  loadTradeRequests()
}

// 处理下拉刷新
function handleRefresh() {
  loadTradeRequests(true)
}

// 处理转模拟操作
function handleConvertToSimulation(request: ITradeRequest) {
  // TODO: 待开发 - 转模拟交易功能
  toast.warning('转模拟功能正在开发中')
  console.log('转模拟:', request)
}

// 处理转交易操作
function handleConvertToTrade(request: ITradeRequest) {
  // TODO: 待开发 - 转实盘交易功能
  toast.warning('转交易功能正在开发中')
  console.log('转交易:', request)
}

// 处理交易请求操作 (V4)
function handleRequestAction(request: ITradeRequest, action: 'fill' | 'reject') {
  currentRequest.value = request
  currentAction.value = action

  if (action === 'fill') {
    feedbackForm.value = {
      quantity: request.requestedQuantity - request.executedQuantity,
      price: request.requestedPrice || 0,
      notes: ''
    }
  } else {
    rejectForm.value = {
      reason: ''
    }
  }

  feedbackDialogVisible.value = true
}

// 处理成交操作
function handleFill(request: ITradeRequest) {
  handleRequestAction(request, 'fill')
}

// 处理拒绝操作
function handleReject(request: ITradeRequest) {
  handleRequestAction(request, 'reject')
}

// 提交反馈 (V4)
async function submitFeedback() {
  if (!currentRequest.value) return

  loading.value = true
  try {
    if (currentAction.value === 'fill') {
      // 验证成交表单
      if (!feedbackForm.value.quantity || feedbackForm.value.quantity <= 0) {
        toast.error('请输入有效的成交数量')
        return
      }
      if (!feedbackForm.value.price || feedbackForm.value.price <= 0) {
        toast.error('请输入有效的成交价格')
        return
      }

      const remainingQuantity = currentRequest.value.requestedQuantity - currentRequest.value.executedQuantity
      if (feedbackForm.value.quantity > remainingQuantity) {
        toast.error(`成交数量不能超过剩余数量 ${remainingQuantity}`)
        return
      }

      // 调用成交接口
      const response = await manualFeedback(currentRequest.value.ID, {
        quantity: Number(feedbackForm.value.quantity),
        price: Number(feedbackForm.value.price),
        notes: feedbackForm.value.notes
      })

      if (response.code === 0) {
        toast.success('成交处理成功')
        feedbackDialogVisible.value = false
        loadTradeRequests(true) // 自动刷新
      } else {
        toast.error(response.msg || '成交失败')
      }
    } else {
      // 验证拒绝表单
      if (!rejectForm.value.reason?.trim()) {
        toast.error('拒绝时请填写原因')
        return
      }

      // 调用拒绝接口
      const response = await rejectTradeRequest(currentRequest.value.ID, {
        reason: rejectForm.value.reason
      })

      if (response.code === 0) {
        toast.success('拒绝处理成功')
        feedbackDialogVisible.value = false
        loadTradeRequests(true) // 自动刷新
      } else {
        toast.error(response.msg || '拒绝失败')
      }
    }
  } catch (error) {
    console.error('提交反馈失败:', error)
    toast.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}


// 处理页面加载参数
onLoad(() => {
  // 设置页面标题
  uni.setNavigationBarTitle({
    title: pageTitle.value
  })
})

// 生命周期
onMounted(() => {
  loadTradeRequests()
})

// 处理下拉刷新
onPullDownRefresh(async () => {
  console.log('下拉刷新开始')
  try {
    await loadTradeRequests(true)
    console.log('下拉刷新完成')
  } catch (error) {
    console.error('下拉刷新失败:', error)
    // 确保即使出错也停止刷新
    uni.stopPullDownRefresh()
  }

  // 添加超时保护，确保刷新状态不会一直持续
  setTimeout(() => {
    if (refreshing.value) {
      console.log('下拉刷新超时，强制停止')
      refreshing.value = false
      uni.stopPullDownRefresh()
    }
  }, 5000) // 5秒超时
})

// 处理上拉加载更多
onReachBottom(() => {
  loadMore()
})
</script>

<template>
  <view class="setter-management-page p-3 min-h-screen bg-gray-100">
    <!-- 筛选条件 -->
    <view class="filter-bar bg-white rounded-lg shadow-sm p-3 mb-3">
      <wd-picker 
        v-model="statusFilter" 
        :columns="statusOptions" 
        placeholder="筛选状态" 
        @confirm="handleStatusFilter" 
      />
    </view>

    <!-- 交易请求列表 -->
    <TradeRequestList
      :requests="tradeRequests"
      :loading="loading"
      :refreshing="refreshing"
      :mode="currentMode"
      @fill="handleFill"
      @reject="handleReject"
      @convert-to-simulation="handleConvertToSimulation"
      @convert-to-trade="handleConvertToTrade"
      @refresh="handleRefresh"
    />

    <!-- 加载更多提示 -->
    <view v-if="tradeRequests.length > 0" class="load-more-tip text-center py-4">
      <view v-if="loadingMore" class="flex justify-center items-center">
        <view class="loading-spinner w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></view>
        <text class="text-sm text-gray-500">{{ loadMoreText }}</text>
      </view>
      <text v-else class="text-xs text-gray-400">{{ loadMoreText }}</text>
    </view>

    <!-- 空状态 -->
    <view v-if="isEmpty" class="empty-state bg-white rounded-lg shadow-sm p-8 text-center">
      <view class="empty-icon w-16 h-16 mx-auto mb-4 opacity-20">
        <svg viewBox="0 0 24 24" fill="currentColor" class="w-full h-full text-gray-400">
          <path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .*********** 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"/>
        </svg>
      </view>
      <text class="text-sm text-gray-400 block mb-4">暂无交易记录</text>
      <wd-button type="primary" size="small" @click="handleRefresh">
        点击刷新
      </wd-button>
    </view>

    <!-- 反馈对话框 -->
    <wd-popup v-model="feedbackDialogVisible" position="center" :close-on-click-modal="false">
      <view class="feedback-dialog">
        <view class="dialog-header">
          <text class="dialog-title">
            {{ currentAction === 'fill' ? '确认成交' : '确认拒绝' }}
          </text>
        </view>

        <view class="dialog-content">
          <view v-if="currentRequest" class="request-summary">
            <text class="summary-text">
              {{ currentRequest.requestType === 'PointPrice' ? '点价' : '洗基差' }} 
              {{ currentRequest.requestedQuantity }} 手
              <text v-if="currentRequest.requestedPrice">@ {{ currentRequest.requestedPrice }}</text>
            </text>
          </view>

          <view v-if="currentAction === 'fill'" class="fill-form">
            <wd-input 
              v-model="feedbackForm.quantity"
              label="成交数量" 
              type="number" 
              placeholder="请输入成交数量"
              :max="currentRequest ? currentRequest.requestedQuantity - currentRequest.executedQuantity : undefined"
            />
            <wd-input 
              v-model="feedbackForm.price"
              label="成交价格" 
              type="number" 
              placeholder="请输入成交价格"
              class="mt-3"
            />
            <wd-textarea 
              v-model="feedbackForm.notes"
              label="备注" 
              placeholder="成交备注（可选）"
              class="mt-3"
            />
          </view>

          <view v-else class="reject-form">
            <wd-textarea
              v-model="rejectForm.reason"
              label="拒绝原因"
              placeholder="请填写拒绝原因"
              required
            />
          </view>
        </view>

        <view class="dialog-actions">
          <wd-button 
            type="info" 
            @click="feedbackDialogVisible = false"
          >
            取消
          </wd-button>
          <wd-button
            :type="currentAction === 'fill' ? 'success' : 'error'"
            @click="submitFeedback"
          >
            确认{{ currentAction === 'fill' ? '成交' : '拒绝' }}
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
.setter-management-page {
  background-color: #f7f8fa;
}

.load-more-tip {
  .loading-spinner {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #409eff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
}

.empty-state {
  .empty-icon {
    width: 128rpx;
    height: 128rpx;
    opacity: 0.3;
  }
}

.feedback-dialog {
  width: 600rpx;
  background: white;
  border-radius: 12rpx;
  overflow: hidden;

  .dialog-header {
    padding: 40rpx 30rpx 20rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .dialog-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .dialog-content {
    padding: 30rpx;

    .request-summary {
      margin-bottom: 30rpx;
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 8rpx;

      .summary-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }

  .dialog-actions {
    display: flex;
    gap: 20rpx;
    padding: 20rpx 30rpx 40rpx;
    justify-content: flex-end;
  }
}
</style>