
# 功能设计文档：点价方管理与交易请求查看器 (V2)

> **文档状态**: 修订版
> **修订日期**: 2025-07-31
> **负责人**: Gemini

## 1. 功能概述

本次需求旨在增强交易双方（Pricer 和 Setter）的用户体验，核心包含三个新功能：

1.  **点价方管理页面 (`pricer-management`)**: 为点价方（Pricer）提供一个专门的管理界面，用于查看自己发起的交易请求。在此页面，点价方可以对处于“执行中”状态的请求执行 **取消** 操作。
2.  **交易请求查看器 (`request-viewer`)**: 创建两个独立的、仅供查看的页面，分别服务于点价方和交易员（Setter）。用户可以在这里按状态分类查看各自相关的所有交易请求。
3.  **分页加载 (Infinite Scroll)**: 在新的查看器页面中，引入分页加载机制，用户可以通过向下滑动列表来加载更多历史数据，优化性能和体验。

## 2. 核心原则：最大化代码复用

经过对现有代码库的详细分析，我们确认**无需任何后端或API层的代码变更**。所有新功能均可通过复用现有接口来实现。

-   **获取列表**: 前后端均已实现 `getTradeRequestsForPricer` 和 `getTradeRequestsForSetter` 的能力，它们支持按 `status` 过滤和标准的分页参数 (`page`, `pageSize`)。我们将利用这两个接口来加载所有新页面的数据。
-   **取消操作**: 前后端均已实现 `cancelTradeRequest` 接口，该接口会验证用户身份（必须是Pricer）和请求状态（必须是Executing），完全满足新功能的需求。

因此，本次开发将**完全聚焦于前端**，通过新增页面和复用组件的方式来完成。

## 3. 前端详细设计

### 3.1. 核心组件改造: `TradeRequestList.vue`

为了在多个页面中统一展示交易请求列表，并根据上下文显示不同的操作按钮，需要对 `app/src/components/TradeRequestList.vue` 组件进行改造。

-   **新增 `mode` 属性**: 为组件增加一个 `mode` 属性，用于控制其显示和行为模式。
    -   `setter` 模式 (默认值): 保持现有行为，显示“成交”、“拒绝”等按钮，用于 `setter-management.vue` 页面。
    -   `pricer` 模式: 当请求状态为 `Executing` 时，显示“取消”按钮。此模式将用于新的 `pricer-management.vue` 页面。
    -   `viewer` 模式: 不显示任何操作按钮，仅作为纯信息展示。此模式将用于两个新的查看器页面。
-   **新增 `cancel` 事件**: 当组件处于 `pricer` 模式时，点击“取消”按钮后，应通过 `emit` 方式向父组件派发一个 `cancel` 事件，并附带当前操作的交易请求对象作为参数。

### 3.2. 新增页面一: 点价方管理 (`pricer-management.vue`)

此页面是 `setter-management.vue` 的点价方版本，允许点价方管理自己发出的请求。

-   **文件路径**: `app/src/pages/trade/pricer-management.vue`。
-   **数据获取**: 调用现有的 `getMyTradeRequestsAsPricer` API 函数来获取数据。支持按状态筛选和下拉刷新。
-   **核心逻辑**:
    -   页面需要实现一个 `handleCancel` 方法。
    -   当监听到 `TradeRequestList` 组件派发的 `cancel` 事件时，调用此方法。
    -   方法内部会弹出确认框，用户确认后，调用现有的 `cancelTradeRequest` API 函数，并传入请求ID。
    -   操作成功后，给出成功提示并刷新列表。
-   **视图渲染**:
    -   页面主体将使用改造后的 `TradeRequestList` 组件。
    -   需要将组件的 `mode` 属性设置为 `pricer`。
    -   需要监听组件的 `cancel` 和 `refresh` 事件。

### 3.3. 新增页面二/三: 交易请求查看器

这两个页面为点价方和交易员提供一个纯粹的、支持无限滚动的历史记录查看界面。

-   **文件路径**:
    -   `app/src/pages/trade/pricer-request-viewer.vue`
    -   `app/src/pages/trade/setter-request-viewer.vue`
-   **数据获取**:
    -   点价方查看器调用 `getMyTradeRequestsAsPricer`。
    -   交易员查看器调用 `getMyTradeRequestsAsSetter`。
-   **核心逻辑 (分页加载)**:
    -   页面需维护分页相关的状态，包括 `page` (当前页码), `pageSize` (每页条数), `hasMore` (是否还有更多数据), 以及 `loading` 状态。
    -   **加载函数**: 实现一个统一的数据加载函数。该函数接收一个 `isRefresh` 布尔参数。如果是刷新操作，则重置页码为1并清空现有列表；否则，页码递增。函数会调用对应的API，并将返回的数据追加到列表中，同时根据返回结果更新 `hasMore` 状态。
    -   **下拉刷新**: 页面需监听 `onPullDownRefresh`生命周期，触发时调用加载函数并指明是刷新操作。
    -   **上拉加载**: 页面需监听 `onReachBottom` 生命周期，当用户滚动到底部且 `hasMore` 为真、当前不处于加载状态时，触发加载函数进行下一页数据的加载。
-   **视图渲染**:
    -   页面主体将使用改造后的 `TradeRequestList` 组件。
    -   需要将组件的 `mode` 属性设置为 `viewer`。
    -   在列表底部，根据加载状态显示 "加载中..." 或 "没有更多数据了" 的提示。

## 4. 实施步骤建议 (纯前端)

1.  **组件改造**: 首先修改 `app/src/components/TradeRequestList.vue`，添加 `mode` 属性和 `cancel` 事件的逻辑。
2.  **创建点价方管理页**:
    -   创建 `pricer-management.vue` 文件。
    -   复用 `setter-management.vue` 的结构，但调用 `getMyTradeRequestsAsPricer` API。
    -   集成 `TradeRequestList` 组件，设置为 `pricer` 模式，并实现取消操作的逻辑。
3.  **创建查看器页面**:
    -   创建 `pricer-request-viewer.vue` 和 `setter-request-viewer.vue` 文件。
    -   实现分页加载和无限滚动的核心逻辑。
    -   集成 `TradeRequestList` 组件，并设置为 `viewer` 模式。
4.  **路由配置**: 在 `pages.config.ts` 中为三个新页面添加入口配置。
5.  **联调测试**: 对所有新页面的功能进行全面测试，包括数据加载、状态筛选、取消操作、下拉刷新和上拉加载。
