<script lang="ts" setup>
import { computed } from 'vue'
import type { ITradeRequest, TradeRequestStatus } from '@/types/trade-request'

// Props 定义
interface TradeRequestItemProps {
  request: ITradeRequest
  /** 显示模式：setter-显示setter操作按钮，pricer-显示取消按钮，viewer-仅查看无按钮 */
  mode?: 'setter' | 'pricer' | 'viewer'
  /** 是否为 setter 管理模式，显示额外操作按钮（向后兼容） */
  isSetterMode?: boolean
}

const props = withDefaults(defineProps<TradeRequestItemProps>(), {
  mode: undefined,
  isSetterMode: false
})

// Emits 定义
const emit = defineEmits<{
  fill: [request: ITradeRequest]
  reject: [request: ITradeRequest]
  convertToSimulation: [request: ITradeRequest]
  convertToTrade: [request: ITradeRequest]
  cancel: [request: ITradeRequest]
}>()

// 获取交易请求状态文本
function getTradeRequestStatusText(status: TradeRequestStatus): string {
  const statusMap: Record<TradeRequestStatus, string> = {
    Executing: '执行中',
    Completed: '已完成',
    Rejected: '已拒绝',
    Cancelled: '已取消',
    Expired: '已过期'
  }
  return statusMap[status] || status
}

// 获取交易请求状态样式
function getTradeRequestStatusClass(status: TradeRequestStatus): string {
  const statusClassMap: Record<TradeRequestStatus, string> = {
    Executing: 'text-blue-500 bg-blue-50',
    Completed: 'text-green-500 bg-green-50',
    Rejected: 'text-red-600 bg-red-50',
    Cancelled: 'text-gray-500 bg-gray-50',
    Expired: 'text-orange-500 bg-orange-50'
  }
  return statusClassMap[status] || 'text-gray-500 bg-gray-50'
}

// 获取执行模式文本
function getExecutionModeText(mode: string): string {
  const modeMap: Record<string, string> = {
    AUTOMATIC: '自动',
    MANUAL: '手动',
    SIMULATED: '模拟'
  }
  return modeMap[mode] || mode
}

// 获取执行模式样式
function getExecutionModeClass(mode: string): string {
  const modeClassMap: Record<string, string> = {
    AUTOMATIC: 'text-purple-500 bg-purple-50',
    MANUAL: 'text-blue-500 bg-blue-50',
    SIMULATED: 'text-orange-500 bg-orange-50'
  }
  return modeClassMap[mode] || 'text-gray-500 bg-gray-50'
}

// 格式化时间
const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  })
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 计算属性
const requestTypeText = computed(() => {
  return props.request.requestType === 'PointPrice' ? '点价' : '洗基差'
})

const createTime = computed(() => {
  return formatTime(props.request.CreatedAt)
})

const statusText = computed(() => {
  return getTradeRequestStatusText(props.request.status)
})

const statusClass = computed(() => {
  return getTradeRequestStatusClass(props.request.status)
})

const executionModeText = computed(() => {
  return getExecutionModeText(props.request.executionMode)
})

const executionModeClass = computed(() => {
  return getExecutionModeClass(props.request.executionMode)
})

const remainingQuantity = computed(() => {
  return props.request.requestedQuantity - props.request.executedQuantity
})

// 计算当前实际模式（兼容旧的 isSetterMode）
const actualMode = computed(() => {
  if (props.mode) {
    return props.mode
  }
  return props.isSetterMode ? 'setter' : 'viewer'
})

// 操作相关计算属性 - 去除对viewer的判断，只根据状态判断是否可操作
const canOperate = computed(() => {
  return props.request.status === 'Executing'
})



const isManualMode = computed(() => {
  return props.request.executionMode === 'MANUAL'
})

const isAutoOrSimMode = computed(() => {
  return props.request.executionMode === 'AUTOMATIC' || props.request.executionMode === 'SIMULATED'
})

// 事件处理 - 去除模式判断，让所有模式都能触发事件
const handleFill = () => {
  emit('fill', props.request)
}

const handleReject = () => {
  emit('reject', props.request)
}

const handleConvertToSimulation = () => {
  emit('convertToSimulation', props.request)
}

const handleConvertToTrade = () => {
  emit('convertToTrade', props.request)
}

const handleCancel = () => {
  emit('cancel', props.request)
}

const hasExecuted = computed(() => {
  return props.request.executedQuantity > 0
})

const expiresTime = computed(() => {
  if (!props.request.expiresAt) return null
  return formatDateTime(props.request.expiresAt)
})

// 判断是否即将过期（1小时内）
const isExpiringSoon = computed(() => {
  if (!props.request.expiresAt || props.request.status !== 'Executing') return false
  const expireTime = new Date(props.request.expiresAt)
  const now = new Date()
  const timeDiff = expireTime.getTime() - now.getTime()
  return timeDiff > 0 && timeDiff <= 60 * 60 * 1000 // 1小时内
})
</script>

<template>
  <view class="trade-request-item card bg-white rounded-lg shadow-sm p-4 mb-3">
    <!-- 卡片头部 -->
    <view class="card-header flex justify-between items-center mb-3">
      <view class="request-info">
        <text class="request-type text-base font-semibold text-gray-800">{{ requestTypeText }}</text>
        <text v-if="actualMode !== 'viewer'" class="request-id text-xs text-gray-500 ml-2">#{{ request.ID }}</text>
        <text class="request-time text-xs text-gray-500 ml-2">{{ createTime }}</text>
      </view>
      <view class="badges flex gap-2">
        <view v-if="actualMode !== 'viewer'" class="execution-mode-badge px-2 py-1 rounded-full text-xs font-medium" :class="executionModeClass">
          {{ executionModeText }}
        </view>
        <view class="status-badge px-2 py-1 rounded-full text-xs font-medium" :class="statusClass">
          {{ statusText }}
        </view>
      </view>
    </view>

    <!-- 卡片主体 -->
    <view class="card-body mb-3">
      <!-- Setter 模式：显示基本信息 -->
      <view v-if="actualMode === 'setter'" class="basic-info grid grid-cols-2 gap-4 mb-3">
        <view class="info-item">
          <text class="info-label text-xs text-gray-500 block mb-1">点价方</text>
          <text class="info-value text-sm font-medium text-gray-800">
            {{ request.pricer?.nickName || request.pricer?.userName || `用户${request.pricerID}` }}
          </text>
        </view>
        <view class="info-item">
          <text class="info-label text-xs text-gray-500 block mb-1">期货合约</text>
          <text class="info-value text-sm font-medium text-gray-800">
            {{ request.instrument?.instrument_name || `合约${request.instrumentRefID}` }}
          </text>
        </view>
      </view>

      <!-- 交易信息 -->
      <view class="trade-info grid grid-cols-2 gap-4">
        <!-- 请求信息 -->
        <view class="request-section">
          <text class="section-label text-xs text-gray-500 block mb-1">请求</text>
          <view class="section-content">
            <text class="quantity text-sm font-medium text-gray-800">{{ request.requestedQuantity }} 手</text>
            <text v-if="request.requestedPrice" class="price text-sm text-gray-600 ml-1">
              @ {{ request.requestedPrice.toFixed(2) }}
            </text>
          </view>
        </view>

        <!-- 成交信息 -->
        <view class="execution-section">
          <text class="section-label text-xs text-gray-500 block mb-1">成交</text>
          <view class="section-content">
            <view v-if="hasExecuted">
              <text class="quantity text-sm font-medium text-gray-800">{{ request.executedQuantity }} 手</text>
              <text class="price text-sm text-gray-600 ml-1">@ {{ request.executedPrice.toFixed(2) }}</text>
            </view>
            <text v-else class="no-execution text-sm text-gray-400">-</text>
          </view>
          <view v-if="actualMode === 'setter' && canOperate && remainingQuantity > 0" class="remaining text-xs text-orange-500 mt-1">
            剩余 {{ remainingQuantity }} 手
          </view>
        </view>
      </view>
    </view>

    <!-- 卡片底部 - 附加信息 -->
    <view class="card-footer">
      <view class="additional-info flex flex-wrap gap-4 text-xs text-gray-500 mb-3">
        <!-- 非 Setter 模式显示执行模式 -->
        <view v-if="actualMode !== 'setter'" class="execution-mode">
          <text class="info-label">执行模式:</text>
          <text class="info-value ml-1">{{ executionModeText }}</text>
        </view>

        <!-- 拒绝原因 -->
        <view v-if="request.status === 'Rejected' && request.rejectionReason" class="rejection-reason">
          <text class="info-label text-red-500">拒绝原因:</text>
          <text class="info-value ml-1 text-red-500">{{ request.rejectionReason }}</text>
        </view>

        <!-- 过期时间 -->
        <view v-if="request.status === 'Executing' && expiresTime" class="expires-time">
          <text class="info-label" :class="{ 'text-orange-500': isExpiringSoon }">过期时间:</text>
          <text class="info-value ml-1" :class="{ 'text-orange-500': isExpiringSoon }">{{ expiresTime }}</text>
          <text v-if="isExpiringSoon" class="warning-text text-orange-500 ml-1">(即将过期)</text>
        </view>
      </view>

      <!-- 备注 -->
      <view v-if="request.notes" class="notes mb-3">
        <text class="info-label text-xs text-gray-500">备注:</text>
        <text class="info-value text-xs text-gray-600 ml-1">{{ request.notes }}</text>
      </view>

      <!-- 操作按钮区域 -->
      <view v-if="canOperate" class="action-buttons">
        <!-- Pricer 模式：只显示取消按钮 -->
        <view v-if="actualMode === 'pricer'" class="pricer-actions flex gap-2">
          <wd-button
            type="warning"
            size="small"
            @click="handleCancel"
          >
            取消
          </wd-button>
        </view>

        <!-- Setter 模式：根据执行模式显示不同按钮 -->
        <template v-else-if="actualMode === 'setter'">
          <!-- 手动模式：显示所有操作按钮 -->
          <view v-if="isManualMode" class="manual-actions flex gap-2 flex-wrap">
            <wd-button
              type="success"
              size="small"
              @click="handleFill"
            >
              成交
            </wd-button>
            <wd-button
              type="error"
              size="small"
              @click="handleReject"
            >
              拒绝
            </wd-button>
            <wd-button
              type="warning"
              size="small"
              @click="handleConvertToSimulation"
            >
              转模拟
            </wd-button>
            <wd-button
              type="primary"
              size="small"
              @click="handleConvertToTrade"
            >
              转交易
            </wd-button>
            <wd-button
              type="warning"
              size="small"
              @click="handleCancel"
            >
              取消
            </wd-button>
          </view>

          <!-- 自动/模拟模式：显示拒绝和取消按钮 -->
          <view v-else-if="isAutoOrSimMode" class="auto-sim-actions flex gap-2">
            <wd-button
              type="error"
              size="small"
              @click="handleReject"
            >
              拒绝
            </wd-button>
            <wd-button
              type="warning"
              size="small"
              @click="handleCancel"
            >
              取消
            </wd-button>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.trade-request-item {
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }
  
  .card-header {
    border-bottom: 1rpx solid #f0f0f0;
    padding-bottom: 12rpx;
    margin-bottom: 12rpx;
    
    .request-type {
      font-size: 32rpx;
      color: #303133;
      font-weight: 600;
    }
    
    .request-id, .request-time {
      font-size: 24rpx;
      color: #909399;
    }
    
    .badges {
      .execution-mode-badge, .status-badge {
        font-size: 22rpx;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
        font-weight: 500;
      }
    }
  }
  
  .card-body {
    .info-label, .section-label {
      font-size: 22rpx;
      color: #909399;
      margin-bottom: 4rpx;
    }
    
    .info-value {
      font-size: 26rpx;
      color: #303133;
      font-weight: 500;
    }
    
    .section-content {
      .quantity {
        font-size: 28rpx;
        color: #303133;
        font-weight: 500;
      }
      
      .price {
        font-size: 26rpx;
        color: #606266;
      }
      
      .no-execution {
        font-size: 28rpx;
        color: #c0c4cc;
      }
      
      .remaining {
        font-weight: 500;
      }
    }
  }
  
  .card-footer {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 12rpx;
    
    .additional-info {
      font-size: 22rpx;
      
      .info-label {
        color: #909399;
        font-weight: 500;
      }
      
      .info-value {
        color: #606266;
      }
      
      .warning-text {
        font-weight: 500;
      }
    }
    
    .notes {
      background-color: #f8f9fa;
      padding: 8rpx 12rpx;
      border-radius: 6rpx;
      margin-top: 8rpx;
      
      .info-label {
        font-size: 22rpx;
        color: #909399;
      }
      
      .info-value {
        font-size: 24rpx;
        color: #606266;
        line-height: 1.4;
      }
    }
    
    .action-buttons {
      .pricer-actions, .manual-actions, .auto-sim-actions {
        :deep(.wd-button) {
          margin-right: 8rpx;
          margin-bottom: 8rpx;
        }
      }
    }
  }
}
</style>